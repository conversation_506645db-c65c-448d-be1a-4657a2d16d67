# Next.js + Supabase 博客系统实现方案

以下文档覆盖从项目初始化到最终满足所有功能点的全过程，结合 Next.js 13 App Router、React 特性与 Supabase 集成实践。

---

## 1. 项目初始化

1. 使用官方脚手架创建项目

   ```bash
   npx create-next-app@latest articles-blog \
     --typescript \
     --eslint \
     --tailwind \
     --app
   ```

   - `--typescript`：启用 TypeScript，提升开发体验与类型安全。
   - `--eslint`：内置 ESLint 配置，保证代码规范。
   - `--tailwind`：集成 Tailwind CSS，快速构建现代化样式。
   - `--app`：启用 Next.js 13 App Router，享受 Server Components、Layout 分割、Suspense 等特性。

2. 安装 Supabase 客户端及依赖

   ```bash
   cd articles-blog
   npm install @supabase/supabase-js dayjs react-markdown rehype-highlight
   ```

   - `@supabase/supabase-js`：操作 Supabase（Postgres + Auth + Storage）。
   - `dayjs`：处理日期展示、格式化。
   - `react-markdown` + `rehype-highlight`：渲染 Markdown 并支持代码高亮。

## 2. 文件与目录结构

```text
articles-blog/
├─ app/
│  ├─ api/
│  │  └─ articles/
│  │     ├─ route.ts       # GET 列表 & POST 创建
│  │     └─ [id]/route.ts  # GET 单篇 & PUT 更新 & DELETE 删除
│  ├─ articles/
│  │  ├─ page.tsx         # 文章列表（SSG+分页+ISR）
│  │  └─ [id]/page.tsx    # 文章详情（SSG+ISR）
│  ├─ create/
│  │  └─ page.tsx         # 新建文章页面
│  ├─ edit/
│  │  └─ [id]/page.tsx    # 编辑文章页面
│  ├─ layout.tsx          # 全局布局：Header、Footer
│  ├─ globals.css         # 全局样式
│  └─ page.tsx            # 首页跳转或 SEO 介绍
├─ components/
│  ├─ Header.tsx
│  ├─ ArticleCard.tsx     # 列表项展示
│  ├─ Pagination.tsx
│  └─ MarkdownRenderer.tsx # Markdown 渲染封装
├─ lib/
│  ├─ supabaseClient.ts   # Supabase 客户端初始化
│  └─ types.ts            # 数据类型定义
├─ styles/
├─ public/
├─ next.config.ts
├─ tsconfig.json
├─ postcss.config.mjs
└─ tailwind.config.js
```
│  ├─ ArticleCard.tsx     # 列表项展示
│  ├─ Pagination.tsx
│  └─ MarkdownRenderer.tsx # Markdown 渲染封装
├─ lib/
│  ├─ supabaseClient.ts   # Supabase 客户端初始化
│  └─ types.ts            # 数据类型定义
├─ styles/
├─ public/
├─ next.config.ts
├─ tsconfig.json
├─ postcss.config.mjs
└─ tailwind.config.js
```

- **分层原因**：
  - `app/`：Next.js 13 App Router，支持 Server Components、嵌套路由、Layout 复用。
  - `components/`：UI 复用、组件分离。
  - `lib/`：业务无关的底层封装（Supabase、类型、工具函数）。

---

## 3. 数据模型与类型定义

在 `lib/types.ts` 中：

```ts
export interface Article {
  id: string;
  title: string;
  content: string;
  tags: string[];
  created_at: string;
  updated_at: string;
}
```

统一类型，方便前后端协作与 TS 检查。

---

## 4. Supabase 客户端封装

在 `lib/supabaseClient.ts`：

```ts
import { createClient } from '@supabase/supabase-js';

export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);
```

集中初始化，全局复用。

---

## 5. API Routes 实现

- `app/api/articles/route.ts`
  - GET：拉取文章列表，支持分页、排序。
  - POST：创建新文章。

- `app/api/articles/[id]/route.ts`
  - GET：拉取单篇文章。
  - PUT：更新文章。
  - DELETE：删除文章。

示例（简化）：

```ts
import { supabase } from '@/lib/supabaseClient';
import { NextResponse } from 'next/server';

export async function GET(req: Request) {
  const { page = '1', pageSize = '10' } = Object.fromEntries(req.nextUrl.searchParams);
  const from = (Number(page) - 1) * Number(pageSize);
  const { data, error, count } = await supabase
    .from('articles')
    .select('*', { count: 'exact' })
    .order('created_at', { ascending: false })
    .range(from, from + Number(pageSize) - 1);
  if (error) throw error;
  return NextResponse.json({ data, count });
}
```

利用 Edge Function + Zero Configuration 部署。

---

## 6. 前端页面实现

1. **文章列表页** (`app/articles/page.tsx`)
   - 使用 `generateStaticParams` 预生成分页路径。
   - 在组件中 `fetch('/api/articles')` 获取数据。
   - `export const revalidate = 60;` 开启 ISR（60s）。

2. **文章详情页** (`app/articles/[id]/page.tsx`)
   - `generateStaticParams` 预渲染部分热门文章。
   - `export const revalidate = 60;`。
   - Markdown 渲染：自定义 `MarkdownRenderer` 组件。

3. **创建 & 编辑 页面**
   - 设置 `"use client"`，使用 React Hook Form 管理表单。
   - 提交时调用对应 API。
   - `useRouter().push` 或 `router.refresh()` 实现跳转与数据刷新。

---

## 7. 进阶功能

- **编辑 & 删除**：
  编辑页面调用 PUT，删除按钮调用 DELETE，并刷新列表页。

- **标签分类**：
  数据库中使用数组或关联表；API 支持 `?tag=xxx` 过滤，前端加筛选 UI。

- **评论功能**：
  新表 `comments`，路由 `app/api/articles/[id]/comments`，前端分页加载 & 提交表单。

- **基础 SEO 优化**：
  每个页面导出 `export const metadata = { title, description, openGraph: {...} }`。

- **Markdown 渲染**：
  `components/MarkdownRenderer.tsx`：

  ```tsx
  import React from 'react';
  import ReactMarkdown from 'react-markdown';
  import rehypeHighlight from 'rehype-highlight';

  export function MarkdownRenderer({ content }: { content: string }) {
    return <ReactMarkdown rehypePlugins={[rehypeHighlight]}>{content}</ReactMarkdown>;
  }
  ```

- **部署到 Vercel**：
  直连 Git 仓库，设置环境变量，自动构建。

---

## 8. 知识点补充

1. **SSG (Static Site Generation)**
   - 预构建页面，快速首屏体验。
   - `generateStaticParams` + `export const revalidate` 实现 ISR。

2. **ISR (Incremental Static Regeneration)**
   - 定时或按需重建页面，无需全量重部署。

3. **App Router vs. Pages Router**
   - Server Component、嵌套路由、Layouts、Streaming、Suspense。

4. **React 特性**
   - Server Component、Client Component (`"use client"`)、Suspense。
   - React Hook Form 高效表单管理。

5. **Supabase**
   - 开箱即用 Postgres + Auth + Storage，前后端统一。

---

## 9. 总结

本方案最大化利用 Next.js 13 + React Server/Client Component，实现高性能、低运维的博客系统。通过 Supabase 一站式服务，快速完成增删改查与进阶功能，满足所有考核要求。
