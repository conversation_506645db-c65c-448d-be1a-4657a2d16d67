// 文章相关接口
import { NextResponse } from "next/server";
import { Article } from "@/lib/types";

// Mock数据
export const mockArticles: Article[] = [
  {
    id: "1",
    title: "Next.js 15 新特性详解",
    content: "# Next.js 15 带来了许多激动人心的新特性\n\n## 1. 改进的App Router\n\n新版本的App Router提供了更好的性能和开发体验...",
    author: "张三",
    tags: ["Next.js", "React", "前端"],
    createdAt: "2024-01-15T10:00:00Z",
    updatedAt: "2024-01-15T10:00:00Z"
  },
  {
    id: "2", 
    title: "React Server Components 实战指南",
    content: "# React Server Components 完全指南\n\n## 什么是Server Components\n\nServer Components是React的一个新特性...",
    author: "李四",
    tags: ["React", "Server Components"],
    createdAt: "2024-01-14T09:30:00Z",
    updatedAt: "2024-01-14T09:30:00Z"
  },
];

// GET /api/articles - 获取文章列表（支持分页、标签筛选）
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get("page") || "1");
  const limit = parseInt(searchParams.get("limit") || "10");
  const tag = searchParams.get("tag");
  
  let filteredArticles = mockArticles;
  if (tag) {
    filteredArticles = mockArticles.filter(article => 
      article.tags.includes(tag)
    );
  }
  
  const startIndex = (page - 1) * limit;
  const paginatedArticles = filteredArticles.slice(startIndex, startIndex + limit);
  
  return NextResponse.json({
    articles: paginatedArticles,
    total: filteredArticles.length,
    page,
    limit,
    hasMore: startIndex + limit < filteredArticles.length
  });
}

// POST /api/articles - 创建文章
export async function POST(request: Request) {
  const body = await request.json();
  const newArticle: Article = {
    id: String(mockArticles.length + 1),
    ...body,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  mockArticles.unshift(newArticle);
  return NextResponse.json(newArticle, { status: 201 });
}