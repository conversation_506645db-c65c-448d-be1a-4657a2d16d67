//  # 页面: 文章列表

import { Article } from "@/lib/types";
import Link from "next/link";
import { Suspense } from "react";

// ISR配置
export const revalidate = 60;

interface SearchParams {
  page?: string;
  tag?: string;
}

export default async function ArticlesPage({
  searchParams,
}: {
  searchParams: SearchParams;
}) {
  const baseURL = process.env.NEXT_PUBLIC_BASE_URL;
  const page = parseInt(searchParams.page || "1");
  const limit = 6;
  
  const url = new URL(`${baseURL}/api/articles`);
  url.searchParams.set("page", page.toString());
  url.searchParams.set("limit", limit.toString());
  if (searchParams.tag) url.searchParams.set("tag", searchParams.tag);

  const response = await fetch(url.toString());
  const data = await response.json();

  return (
    <div className="max-w-6xl mx-auto">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">文章列表</h1>
        <p className="text-gray-600">发现优质技术文章，分享编程心得</p>
      </div>

      {/* 文章网格布局 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {data.articles?.map((article: Article) => (
          <article
            key={article.id}
            className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden border border-gray-200"
          >
            {/* 文章内容 */}
            <div className="p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2 hover:text-blue-600 transition-colors">
                <Link href={`/articles/${article.id}`}>
                  {article.title}
                </Link>
              </h2>
              
              <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                {article.content.replace(/[#*`]/g, '').substring(0, 120)}...
              </p>

              {/* 标签 */}
              <div className="flex flex-wrap gap-2 mb-4">
                {article.tags?.slice(0, 3).map((tag) => (
                  <Link
                    key={tag}
                    href={`/articles?tag=${encodeURIComponent(tag)}`}
                    className="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full hover:bg-blue-100 transition-colors"
                  >
                    {tag}
                  </Link>
                ))}
              </div>

              {/* 作者和时间 */}
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                  {article.author}
                </span>
                <time dateTime={article.createdAt}>
                  {new Date(article.createdAt).toLocaleDateString('zh-CN')}
                </time>
              </div>

              {/* 操作按钮 */}
              <div className="flex gap-2">
                <Link
                  href={`/articles/${article.id}`}
                  className="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
                >
                  阅读全文
                </Link>
                <button className="px-3 py-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </div>
          </article>
        ))}
      </div>

      {/* 分页组件 */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-700">
          显示第 {(page - 1) * limit + 1} - {Math.min(page * limit, data.total)} 条，
          共 {data.total} 条文章
        </div>
        
        <div className="flex gap-2">
          {page > 1 && (
            <Link
              href={`/articles?page=${page - 1}${searchParams.tag ? `&tag=${searchParams.tag}` : ''}`}
              className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            >
              上一页
            </Link>
          )}
          
          {/* 页码 */}
          <div className="flex gap-1">
            {Array.from({ length: Math.min(5, Math.ceil(data.total / limit)) }, (_, i) => {
              const pageNum = Math.max(1, page - 2) + i;
              if (pageNum > Math.ceil(data.total / limit)) return null;
              
              return (
                <Link
                  key={pageNum}
                  href={`/articles?page=${pageNum}${searchParams.tag ? `&tag=${searchParams.tag}` : ''}`}
                  className={`px-3 py-2 rounded-md transition-colors ${
                    pageNum === page
                      ? 'bg-blue-600 text-white'
                      : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {pageNum}
                </Link>
              );
            })}
          </div>

          {data.hasMore && (
            <Link
              href={`/articles?page=${page + 1}${searchParams.tag ? `&tag=${searchParams.tag}` : ''}`}
              className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            >
              下一页
            </Link>
          )}
        </div>
      </div>

      {/* 空状态 */}
      {(!data.articles || data.articles.length === 0) && (
        <div className="text-center py-12">
          <svg className="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无文章</h3>
          <p className="text-gray-500 mb-4">还没有发布任何文章</p>
          <Link
            href="/create"
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            写第一篇文章
          </Link>
        </div>
      )}
    </div>
  );
}
